USE [MusicSchool]
GO

-- Add sidebar theme configuration to UI group (300)
-- Check if the configuration already exists before inserting
IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 300 AND [Key] = 'SidebarTheme')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType], [CreatedUTC])
    VALUES (300, 'SidebarTheme', '1', 'Sidebar color theme selection (1=BluePurple, 2=BlueGray, 3=<PERSON>, 4=<PERSON>, 5=<PERSON>, 6=<PERSON>Gray, 7=NavyBlue, 8=Teal, 9=<PERSON>, 10=<PERSON>Blue)', 'int', GETUTCDATE());
    
    PRINT 'Sidebar theme configuration added successfully.';
END
ELSE
BEGIN
    PRINT 'Sidebar theme configuration already exists.';
END

-- Display current UI configurations
SELECT [ConfigId], [GroupId], [Key], [Value], [Description], [DataType], [CreatedUTC], [UpdatedUTC]
FROM [dbo].[Config] 
WHERE [GroupId] = 300
ORDER BY [Key];

GO
