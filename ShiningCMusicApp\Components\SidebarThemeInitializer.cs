using Microsoft.AspNetCore.Components;
using ShiningCMusicApp.Services.Interfaces;

namespace ShiningCMusicApp.Components
{
    public class SidebarThemeInitializer : ComponentBase
    {
        [Inject] private ISidebarThemeService SidebarThemeService { get; set; } = default!;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await SidebarThemeService.InitializeAsync();
            }
        }
    }
}
