using Microsoft.JSInterop;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Implementations
{
    public class SidebarThemeService : ISidebarThemeService
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly ILogger<SidebarThemeService> _logger;
        private readonly IConfigurationService _configService;
        private SidebarTheme _currentTheme = SidebarTheme.BluePurple;

        public SidebarThemeService(IJSRuntime jsRuntime, ILogger<SidebarThemeService> logger, IConfigurationService configService)
        {
            _jsRuntime = jsRuntime;
            _logger = logger;
            _configService = configService;
        }

        public async Task ApplyThemeAsync(SidebarTheme theme)
        {
            try
            {
                _currentTheme = theme;
                var cssGradient = theme.GetCssGradient();
                
                // Generate CSS to update sidebar colors
                var css = $@"
                    .sidebar {{
                        background-image: {cssGradient} !important;
                    }}
                    .mobile-menu {{
                        background-image: {cssGradient} !important;
                    }}
                    .offcanvas-body {{
                        background-image: {cssGradient} !important;
                    }}
                    .offcanvas-header {{
                        background-image: {cssGradient} !important;
                    }}
                ";

                // Inject CSS into the document
                await _jsRuntime.InvokeVoidAsync("applySidebarTheme", css);
                
                _logger.LogInformation("Applied sidebar theme: {Theme}", theme);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to apply sidebar theme: {Theme}", theme);
            }
        }

        public async Task<SidebarTheme> GetCurrentThemeAsync()
        {
            try
            {
                var config = await _configService.GetConfigurationAsync();
                return config?.SidebarTheme ?? SidebarTheme.BluePurple;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get current sidebar theme from configuration");
                return SidebarTheme.BluePurple;
            }
        }

        public async Task InitializeAsync()
        {
            try
            {
                var theme = await GetCurrentThemeAsync();
                await ApplyThemeAsync(theme);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize sidebar theme service");
            }
        }
    }
}
