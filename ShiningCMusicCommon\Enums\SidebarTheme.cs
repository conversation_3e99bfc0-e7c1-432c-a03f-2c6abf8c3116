using System.ComponentModel;

namespace ShiningCMusicCommon.Enums
{
    public enum SidebarTheme
    {
        [Description("Blue to Purple (Default)")]
        BluePurple = 1,
        
        [Description("Blue Gray")]
        BlueGray = 2,
        
        [Description("Green")]
        Green = 3,
        
        [Description("Red")]
        Red = 4,
        
        [Description("Purple")]
        Purple = 5,
        
        [Description("Dark Gray")]
        DarkGray = 6,
        
        [Description("Navy Blue")]
        NavyBlue = 7,
        
        [Description("Teal")]
        Teal = 8,
        
        [Description("Orange")]
        Orange = 9,
        
        [Description("Dark Blue")]
        DarkBlue = 10
    }
    
    public static class SidebarThemeExtensions
    {
        public static string GetCssGradient(this SidebarTheme theme)
        {
            return theme switch
            {
                SidebarTheme.BluePurple => "linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%)",
                SidebarTheme.BlueGray => "linear-gradient(180deg, #2c3e50 0%, #34495e 70%)",
                SidebarTheme.Green => "linear-gradient(180deg, #27ae60 0%, #2ecc71 70%)",
                SidebarTheme.Red => "linear-gradient(180deg, #e74c3c 0%, #c0392b 70%)",
                SidebarTheme.Purple => "linear-gradient(180deg, #8e44ad 0%, #9b59b6 70%)",
                SidebarTheme.DarkGray => "linear-gradient(180deg, #34495e 0%, #2c3e50 70%)",
                SidebarTheme.NavyBlue => "linear-gradient(180deg, #2c3e50 0%, #1a252f 70%)",
                SidebarTheme.Teal => "linear-gradient(180deg, #16a085 0%, #1abc9c 70%)",
                SidebarTheme.Orange => "linear-gradient(180deg, #e67e22 0%, #f39c12 70%)",
                SidebarTheme.DarkBlue => "linear-gradient(180deg, #1a252f 0%, #2c3e50 70%)",
                _ => "linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%)"
            };
        }
        
        public static string GetPreviewColor(this SidebarTheme theme)
        {
            return theme switch
            {
                SidebarTheme.BluePurple => "#3a0647",
                SidebarTheme.BlueGray => "#34495e",
                SidebarTheme.Green => "#2ecc71",
                SidebarTheme.Red => "#c0392b",
                SidebarTheme.Purple => "#9b59b6",
                SidebarTheme.DarkGray => "#2c3e50",
                SidebarTheme.NavyBlue => "#1a252f",
                SidebarTheme.Teal => "#1abc9c",
                SidebarTheme.Orange => "#f39c12",
                SidebarTheme.DarkBlue => "#2c3e50",
                _ => "#3a0647"
            };
        }
        
        public static string GetDescription(this SidebarTheme theme)
        {
            var field = theme.GetType().GetField(theme.ToString());
            var attribute = field?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                .FirstOrDefault() as DescriptionAttribute;
            return attribute?.Description ?? theme.ToString();
        }
    }
}
