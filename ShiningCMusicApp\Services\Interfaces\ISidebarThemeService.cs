using ShiningCMusicCommon.Enums;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface ISidebarThemeService
    {
        /// <summary>
        /// Applies the specified sidebar theme by injecting CSS into the document
        /// </summary>
        /// <param name="theme">The sidebar theme to apply</param>
        Task ApplyThemeAsync(SidebarTheme theme);
        
        /// <summary>
        /// Gets the current sidebar theme from configuration
        /// </summary>
        /// <returns>The current sidebar theme</returns>
        Task<SidebarTheme> GetCurrentThemeAsync();
        
        /// <summary>
        /// Initializes the sidebar theme service and applies the current theme
        /// </summary>
        Task InitializeAsync();
    }
}
