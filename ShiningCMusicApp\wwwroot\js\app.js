// Shining C Music App JavaScript
// Main application JavaScript file

// Mobile Menu Functions
function openMobileMenu() {
    console.log('Opening mobile menu...');
    const menu = document.getElementById('mobileMenu');
    const overlay = document.getElementById('mobileMenuOverlay');

    if (menu && overlay) {
        menu.classList.add('show');
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden'; // Prevent scrolling
        console.log('Mobile menu opened');
    } else {
        console.error('Menu or overlay not found');
    }
}

function closeMobileMenu() {
    console.log('Closing mobile menu...');
    const menu = document.getElementById('mobileMenu');
    const overlay = document.getElementById('mobileMenuOverlay');

    if (menu && overlay) {
        menu.classList.remove('show');
        overlay.classList.remove('show');
        document.body.style.overflow = ''; // Restore scrolling
        console.log('Mobile menu closed');
    }
}

// Session Timeout Management
window.sessionTimeout = {
    dotNetRef: null,
    isInitialized: false,
    lastActivity: Date.now(),
    activityThrottle: 30000, // Only report activity every 30 seconds to avoid excessive calls

    initialize: function(dotNetReference) {
        this.dotNetRef = dotNetReference;
        this.isInitialized = true;
        this.setupActivityListeners();
        console.log('Session timeout JavaScript initialized');
    },

    setupActivityListeners: function() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

        events.forEach(event => {
            document.addEventListener(event, this.onActivity.bind(this), true);
        });
    },

    onActivity: function() {
        if (!this.isInitialized || !this.dotNetRef) return;

        const now = Date.now();
        // Throttle activity reporting to avoid excessive calls
        if (now - this.lastActivity > this.activityThrottle) {
            this.lastActivity = now;
            try {
                this.dotNetRef.invokeMethodAsync('OnUserActivity');
            } catch (error) {
                console.error('Error calling OnUserActivity:', error);
            }
        }
    }
};

/**
 * Downloads a file from base64 data to the user's browser
 * @param {string} filename - The name of the file to download
 * @param {string} base64Data - Base64 encoded file content
 */
function downloadFile(filename, base64Data) {
    try {
        // Convert base64 to blob
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Error downloading file:', error);
        throw error;
    }
}

// Also assign to window object for extra safety
window.downloadFile = downloadFile;

// Test function to verify JavaScript is loaded
window.testDownloadFunction = function() {
    console.log('testDownloadFunction called');
    console.log('downloadFile function exists:', typeof downloadFile);
    console.log('window.downloadFile exists:', typeof window.downloadFile);
    return typeof downloadFile === 'function';
};

// Sidebar Theme Functions
function applySidebarTheme(css) {
    console.log('Applying sidebar theme...');

    // Remove existing theme style if it exists
    const existingStyle = document.getElementById('sidebar-theme-style');
    if (existingStyle) {
        existingStyle.remove();
    }

    // Create new style element
    const style = document.createElement('style');
    style.id = 'sidebar-theme-style';
    style.textContent = css;

    // Append to head
    document.head.appendChild(style);

    console.log('Sidebar theme applied successfully');
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, mobile menu ready');
    const menuButton = document.querySelector('.mobile-menu-btn');
    const menu = document.getElementById('mobileMenu');
    console.log('Menu button found:', !!menuButton);
    console.log('Menu element found:', !!menu);

    // Add keyboard support for accessibility
    document.addEventListener('keydown', function(event) {
        // Close menu when Escape key is pressed
        if (event.key === 'Escape') {
            const menu = document.getElementById('mobileMenu');
            if (menu && menu.classList.contains('show')) {
                closeMobileMenu();
            }
        }
    });
});
